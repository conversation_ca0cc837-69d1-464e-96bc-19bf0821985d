// persistent-sidebar.js - Solution définitive pour éviter le scintillement de la sidebar

(function() {
    'use strict';
    
    // Namespace global pour éviter les conflits
    window.PERSISTENT_SIDEBAR = window.PERSISTENT_SIDEBAR || {
        initialized: false,
        sidebarElement: null,
        navBuilt: false,
        currentPage: null
    };
    
    const PS = window.PERSISTENT_SIDEBAR;
    
    /**
     * Crée la sidebar une seule fois
     */
    function createSidebar() {
        if (PS.sidebarElement) {
            return PS.sidebarElement;
        }
        
        const aside = document.createElement('aside');
        aside.className = 'w-64 bg-gray-800 text-white flex flex-col flex-shrink-0';
        aside.innerHTML = `
            <div class="p-4 text-2xl font-bold border-b border-gray-700">GestionPro</div>
            <nav id="main-nav" class="flex-grow"></nav>
        `;
        
        PS.sidebarElement = aside;
        return aside;
    }
    
    /**
     * Insère la sidebar dans le conteneur de la page actuelle
     */
    function insertSidebar() {
        const container = document.getElementById('sidebar-container');
        if (!container) {
            console.warn('Conteneur sidebar-container non trouvé');
            return false;
        }
        
        // Si la sidebar est déjà dans ce conteneur, ne rien faire
        if (container.contains(PS.sidebarElement)) {
            console.log('Sidebar déjà présente dans le conteneur');
            return true;
        }
        
        // Créer ou récupérer la sidebar
        const sidebar = createSidebar();
        
        // Retirer la sidebar de son conteneur précédent s'il existe
        if (sidebar.parentNode) {
            sidebar.parentNode.removeChild(sidebar);
        }
        
        // Insérer dans le nouveau conteneur
        container.appendChild(sidebar);
        console.log('Sidebar insérée dans le nouveau conteneur');
        return true;
    }
    
    /**
     * Construit la navigation une seule fois
     */
    async function buildNavigationOnce(activePage) {
        const navContainer = document.getElementById('main-nav');
        if (!navContainer) {
            console.warn('Conteneur main-nav non trouvé');
            return;
        }
        
        // Si la navigation est déjà construite, juste mettre à jour le lien actif
        if (PS.navBuilt && navContainer.children.length > 0) {
            console.log('Navigation déjà construite, mise à jour du lien actif');
            if (window.updateActiveLink) {
                window.updateActiveLink(activePage);
            }
            return;
        }
        
        // Construire la navigation
        if (window.buildNavigation) {
            console.log('Construction de la navigation pour la première fois');
            await window.buildNavigation(activePage);
            PS.navBuilt = true;
        } else {
            console.warn('Fonction buildNavigation non disponible');
        }
    }
    
    /**
     * Initialise la sidebar persistante pour une page
     */
    async function initializePersistentSidebar(pageName) {
        try {
            // Déterminer le nom de la page si non fourni
            if (!pageName) {
                pageName = window.location.pathname.split('/').pop().replace('.html', '') || 'index';
            }
            
            console.log(`Initialisation de la sidebar persistante pour: ${pageName}`);
            
            // Insérer la sidebar
            const inserted = insertSidebar();
            if (!inserted) {
                console.error('Impossible d\'insérer la sidebar');
                return;
            }
            
            // Attendre que le DOM soit stable
            await new Promise(resolve => setTimeout(resolve, 100));
            
            // Construire la navigation
            await buildNavigationOnce(pageName);
            
            // Mettre à jour la page actuelle
            PS.currentPage = pageName;
            PS.initialized = true;
            
            console.log('Sidebar persistante initialisée avec succès');
            
        } catch (error) {
            console.error('Erreur lors de l\'initialisation de la sidebar persistante:', error);
        }
    }
    
    /**
     * Met à jour seulement le lien actif (pour les changements de page)
     */
    function updateActivePage(pageName) {
        if (!pageName) {
            pageName = window.location.pathname.split('/').pop().replace('.html', '') || 'index';
        }
        
        if (PS.currentPage === pageName) {
            console.log('Page déjà active, pas de mise à jour nécessaire');
            return;
        }
        
        console.log(`Mise à jour de la page active: ${PS.currentPage} -> ${pageName}`);
        
        if (window.updateActiveLink) {
            window.updateActiveLink(pageName);
        }
        
        PS.currentPage = pageName;
    }
    
    /**
     * Réinitialise complètement la sidebar (pour le debug)
     */
    function resetPersistentSidebar() {
        PS.initialized = false;
        PS.navBuilt = false;
        PS.currentPage = null;
        if (PS.sidebarElement && PS.sidebarElement.parentNode) {
            PS.sidebarElement.parentNode.removeChild(PS.sidebarElement);
        }
        PS.sidebarElement = null;
        console.log('Sidebar persistante réinitialisée');
    }
    
    // Exposer les fonctions globalement
    window.initializePersistentSidebar = initializePersistentSidebar;
    window.updateActivePage = updateActivePage;
    window.resetPersistentSidebar = resetPersistentSidebar;
    
    // Auto-initialisation
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            initializePersistentSidebar();
        });
    } else {
        // DOM déjà chargé
        setTimeout(() => {
            initializePersistentSidebar();
        }, 50);
    }
    
})();
