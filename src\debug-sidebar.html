<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Sidebar - Système de Gestion</title>
    <link href="./css/output.css" rel="stylesheet">
</head>
<body class="bg-gray-100 dark:bg-gray-900 flex h-screen">
    <div id="sidebar-container"></div>

    <main class="flex-1 p-8 overflow-y-auto">
        <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-6">Debug de la Sidebar</h1>
        
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">État de la Sidebar</h2>
            
            <div class="space-y-4">
                <div>
                    <button onclick="checkSidebarState()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        Vérifier l'état
                    </button>
                    <button onclick="resetSidebar()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 ml-2">
                        Réinitialiser
                    </button>
                </div>
                
                <div id="debug-info" class="bg-gray-50 p-4 rounded border text-sm font-mono">
                    <p>Cliquez sur "Vérifier l'état" pour voir les informations de debug</p>
                </div>
            </div>
        </div>
        
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 class="text-yellow-800 font-medium mb-2">Test de navigation :</h3>
            <div class="flex flex-wrap gap-2">
                <a href="index.html" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">Dashboard</a>
                <a href="caisse.html" class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600">Caisse</a>
                <a href="products.html" class="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600">Produits</a>
                <a href="clients.html" class="bg-teal-500 text-white px-3 py-1 rounded text-sm hover:bg-teal-600">Clients</a>
                <a href="history.html" class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">Historique</a>
                <a href="debug-sidebar.html" class="bg-orange-500 text-white px-3 py-1 rounded text-sm hover:bg-orange-600">Debug</a>
            </div>
            <p class="text-yellow-700 text-sm mt-2">
                Observez si la sidebar disparaît/réapparaît lors des changements de page
            </p>
        </div>
    </main>

    <script src="./js/i18n.js"></script>
    <script src="./js/notifications.js"></script>
    <script src="./js/persistent-sidebar.js"></script>
    <script src="./js/layout.js"></script>
    
    <script>
        function checkSidebarState() {
            const debugInfo = document.getElementById('debug-info');
            const sidebar = document.querySelector('aside');
            const nav = document.getElementById('main-nav');
            const container = document.getElementById('sidebar-container');
            
            let info = '';
            info += `Sidebar présente: ${sidebar ? '✅ Oui' : '❌ Non'}\n`;
            info += `Navigation présente: ${nav ? '✅ Oui' : '❌ Non'}\n`;
            info += `Navigation construite: ${nav && nav.children.length > 0 ? '✅ Oui (' + nav.children.length + ' liens)' : '❌ Non'}\n`;
            info += `Conteneur présent: ${container ? '✅ Oui' : '❌ Non'}\n`;
            
            // Variables globales
            if (typeof window.sidebarLoaded !== 'undefined') {
                info += `sidebarLoaded: ${window.sidebarLoaded ? '✅ True' : '❌ False'}\n`;
            }
            if (typeof window.sidebarInitialized !== 'undefined') {
                info += `sidebarInitialized: ${window.sidebarInitialized ? '✅ True' : '❌ False'}\n`;
            }
            
            debugInfo.innerHTML = '<pre>' + info + '</pre>';
        }
        
        function resetSidebar() {
            if (window.resetPersistentSidebar) {
                window.resetPersistentSidebar();
            }
            if (window.resetNavigation) {
                window.resetNavigation();
            }

            // Recharger la page pour tester
            setTimeout(() => {
                window.location.reload();
            }, 500);
        }
        
        // Vérification automatique au chargement
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(checkSidebarState, 1000);
        });
    </script>
</body>
</html>
