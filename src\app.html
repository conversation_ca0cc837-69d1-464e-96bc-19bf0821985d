<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self'">
    <title data-i18n="app_title">GestionPro - Système de Gestion</title>

    <!-- Pré-chargement des traductions pour éviter le flash -->
    <script>
        // Pré-chargement immédiat de la langue depuis localStorage
        (function() {
            const savedLang = localStorage.getItem('app-language') || 'fr';
            document.documentElement.lang = savedLang;
            document.documentElement.dir = savedLang === 'ar' ? 'rtl' : 'ltr';
            if (savedLang === 'ar') {
                document.documentElement.classList.add('rtl');
            }
        })();
    </script>

    <link href="./css/output.css" rel="stylesheet">
    <style>
        /* Styles pour le mode sombre */
        .dark {
            color-scheme: dark;
        }

        /* Amélioration du contraste pour le menu */
        .dark .nav-link {
            color: #e5e7eb;
        }

        .dark .nav-link:hover {
            color: #ffffff;
            background-color: #374151;
        }

        /* Styles pour les liens actifs */
        .active-nav-link {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white !important;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
            border-left: 4px solid #60a5fa;
        }

        .dark .active-nav-link {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.5);
        }

        /* Animation pour les transitions de page */
        #page-content {
            transition: opacity 0.2s ease-in-out;
        }

        #page-content.loading {
            opacity: 0.7;
        }

        /* Loader pour les transitions */
        .page-loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
        }

        .page-loader.active {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Styles pour les transitions fluides */
        .nav-link {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Éviter le scintillement lors du changement de page */
        .sidebar-fixed {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 10;
        }

        .content-with-sidebar {
            margin-left: 16rem; /* w-64 = 16rem */
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar-fixed {
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
            }

            .sidebar-fixed.mobile-open {
                transform: translateX(0);
            }

            .content-with-sidebar {
                margin-left: 0;
            }
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 min-h-screen">
    <!-- Sidebar fixe -->
    <aside class="sidebar-fixed w-64 h-screen bg-gray-800 text-white flex flex-col flex-shrink-0">
        <div class="p-4 text-2xl font-bold border-b border-gray-700">GestionPro</div>
        <nav id="main-nav" class="flex-grow"></nav>
    </aside>

    <!-- Bouton menu mobile -->
    <button id="mobile-menu-btn" class="md:hidden fixed top-4 left-4 z-20 bg-gray-800 text-white p-2 rounded-lg">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
    </button>

    <!-- Contenu principal -->
    <main class="content-with-sidebar min-h-screen">
        <!-- Loader pour les transitions -->
        <div class="page-loader">
            <div class="spinner"></div>
        </div>

        <!-- Conteneur du contenu de la page -->
        <div id="page-content" class="p-8">
            <!-- Le contenu des pages sera injecté ici -->
        </div>
    </main>

    <!-- Scripts de base -->
    <script src="./js/i18n.js"></script>
    <script src="./js/preloader.js"></script>
    <script src="./js/notifications.js"></script>
    <script src="./js/layout.js"></script>
    <script src="./js/spa-navigation.js"></script>
    <script src="./js/spa-router.js"></script>
</body>
</html>
