<div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="pos_title">💰 Point de Vente</h1>
    <div class="flex gap-3">
        <button id="newSaleBtn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition">
            Nouvelle Vente
        </button>
        <button id="historyBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
            Historique
        </button>
    </div>
</div>

<!-- Interface de caisse -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Panneau de recherche et produits -->
    <div class="lg:col-span-2">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div class="mb-4">
                <input type="text" id="productSearch" 
                       class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-blue-500"
                       placeholder="Rechercher un produit par nom ou code-barres...">
            </div>
            
            <!-- Grille des produits -->
            <div id="productsGrid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
                <!-- Produits seront chargés dynamiquement -->
            </div>
        </div>
    </div>

    <!-- Panier et total -->
    <div class="lg:col-span-1">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4">Panier</h2>
            
            <!-- Liste des articles -->
            <div id="cartItems" class="space-y-2 mb-4 max-h-64 overflow-y-auto">
                <!-- Articles du panier -->
            </div>
            
            <!-- Total -->
            <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                <div class="flex justify-between items-center text-lg font-bold">
                    <span>Total:</span>
                    <span id="cartTotal" class="text-green-600">0.00 MAD</span>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="mt-4 space-y-2">
                <button id="cashPaymentBtn" class="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition font-semibold">
                    Paiement Espèces
                </button>
                <button id="creditPaymentBtn" class="w-full bg-orange-600 text-white py-3 rounded-lg hover:bg-orange-700 transition font-semibold">
                    Vente à Crédit
                </button>
                <button id="clearCartBtn" class="w-full bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 transition">
                    Vider le Panier
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Ventes Aujourd'hui</p>
                <p id="todaySales" class="text-lg font-semibold text-gray-900 dark:text-white">0.00 MAD</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Transactions</p>
                <p id="todayTransactions" class="text-lg font-semibold text-gray-900 dark:text-white">0</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <div class="flex items-center">
            <div class="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Crédits Accordés</p>
                <p id="todayCredits" class="text-lg font-semibold text-gray-900 dark:text-white">0.00 MAD</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Moyenne/Transaction</p>
                <p id="averageTransaction" class="text-lg font-semibold text-gray-900 dark:text-white">0.00 MAD</p>
            </div>
        </div>
    </div>
</div>

<!-- Modals seront ajoutés par le script JavaScript -->
