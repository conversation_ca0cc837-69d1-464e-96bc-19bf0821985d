<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final - Sidebar Persistante</title>
    <link href="./css/output.css" rel="stylesheet">
</head>
<body class="bg-gray-100 dark:bg-gray-900 flex h-screen">
    <div id="sidebar-container"></div>

    <main class="flex-1 p-8 overflow-y-auto">
        <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-6">🎯 Test Final - Sidebar Persistante</h1>
        
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-green-800 mb-4">✅ Solution Implémentée</h2>
            <div class="space-y-2 text-green-700">
                <p><strong>✅ Sidebar persistante créée</strong> - Un seul élément DOM réutilisé</p>
                <p><strong>✅ Navigation construite une seule fois</strong> - Pas de reconstruction</p>
                <p><strong>✅ État global maintenu</strong> - Variables dans window.PERSISTENT_SIDEBAR</p>
                <p><strong>✅ Mise à jour intelligente</strong> - Seul le lien actif change</p>
            </div>
        </div>
        
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-blue-800 mb-4">🧪 Test de Navigation</h2>
            <p class="text-blue-700 mb-4">Cliquez sur ces liens et observez que la sidebar ne disparaît plus :</p>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                <a href="index.html" class="bg-blue-500 text-white px-4 py-2 rounded text-center hover:bg-blue-600 transition-colors">
                    🏠 Dashboard
                </a>
                <a href="caisse.html" class="bg-green-500 text-white px-4 py-2 rounded text-center hover:bg-green-600 transition-colors">
                    💰 Caisse
                </a>
                <a href="products.html" class="bg-purple-500 text-white px-4 py-2 rounded text-center hover:bg-purple-600 transition-colors">
                    📦 Produits
                </a>
                <a href="clients.html" class="bg-teal-500 text-white px-4 py-2 rounded text-center hover:bg-teal-600 transition-colors">
                    👥 Clients
                </a>
                <a href="history.html" class="bg-gray-500 text-white px-4 py-2 rounded text-center hover:bg-gray-600 transition-colors">
                    📊 Historique
                </a>
                <a href="test-final.html" class="bg-orange-500 text-white px-4 py-2 rounded text-center hover:bg-orange-600 transition-colors">
                    🧪 Test Final
                </a>
            </div>
        </div>
        
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-yellow-800 mb-4">📋 Instructions de Test</h2>
            <ol class="text-yellow-700 space-y-2">
                <li><strong>1.</strong> Observez la sidebar à gauche - elle doit se charger sans scintillement</li>
                <li><strong>2.</strong> Cliquez sur différents liens de navigation dans la sidebar</li>
                <li><strong>3.</strong> Vérifiez que la sidebar reste stable (pas de disparition/réapparition)</li>
                <li><strong>4.</strong> Testez plusieurs changements de page consécutifs rapidement</li>
                <li><strong>5.</strong> Observez que seul le lien actif change de couleur</li>
            </ol>
        </div>
        
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">🔧 Debug Info</h2>
            <div class="space-y-2">
                <button onclick="showDebugInfo()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                    Afficher les informations de debug
                </button>
                <div id="debug-output" class="bg-white p-4 rounded border text-sm font-mono hidden">
                    <!-- Debug info will be displayed here -->
                </div>
            </div>
        </div>
    </main>

    <script src="./js/i18n.js"></script>
    <script src="./js/notifications.js"></script>
    <script src="./js/persistent-sidebar.js"></script>
    <script src="./js/layout.js"></script>
    
    <script>
        function showDebugInfo() {
            const output = document.getElementById('debug-output');
            const PS = window.PERSISTENT_SIDEBAR;
            const NS = window.NAVIGATION_STATE;
            
            let info = '=== PERSISTENT SIDEBAR STATE ===\n';
            info += `Initialized: ${PS ? PS.initialized : 'N/A'}\n`;
            info += `Navigation Built: ${PS ? PS.navBuilt : 'N/A'}\n`;
            info += `Current Page: ${PS ? PS.currentPage : 'N/A'}\n`;
            info += `Sidebar Element: ${PS && PS.sidebarElement ? 'Present' : 'Missing'}\n\n`;
            
            info += '=== NAVIGATION STATE ===\n';
            info += `Initialized: ${NS ? NS.initialized : 'N/A'}\n`;
            info += `Built: ${NS ? NS.built : 'N/A'}\n\n`;
            
            info += '=== DOM STATE ===\n';
            const sidebar = document.querySelector('aside');
            const nav = document.getElementById('main-nav');
            info += `Sidebar in DOM: ${sidebar ? 'Yes' : 'No'}\n`;
            info += `Navigation in DOM: ${nav ? 'Yes' : 'No'}\n`;
            info += `Navigation Links: ${nav ? nav.children.length : 0}\n`;
            
            output.innerHTML = '<pre>' + info + '</pre>';
            output.classList.remove('hidden');
        }
        
        // Auto-show debug info after 2 seconds
        setTimeout(() => {
            showDebugInfo();
        }, 2000);
    </script>
</body>
</html>
