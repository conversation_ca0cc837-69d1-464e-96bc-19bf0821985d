<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Templates - GestionPro</title>
    <link href="./css/output.css" rel="stylesheet">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-6">🧪 Test de Chargement des Templates</h1>
        
        <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
            <button onclick="testTemplate('dashboard')" class="bg-blue-500 text-white p-3 rounded-lg hover:bg-blue-600">
                Dashboard
            </button>
            <button onclick="testTemplate('caisse')" class="bg-green-500 text-white p-3 rounded-lg hover:bg-green-600">
                Caisse
            </button>
            <button onclick="testTemplate('products')" class="bg-purple-500 text-white p-3 rounded-lg hover:bg-purple-600">
                Produits
            </button>
            <button onclick="testTemplate('clients')" class="bg-teal-500 text-white p-3 rounded-lg hover:bg-teal-600">
                Clients
            </button>
            <button onclick="testTemplate('credits')" class="bg-red-500 text-white p-3 rounded-lg hover:bg-red-600">
                Crédits
            </button>
            <button onclick="testTemplate('history')" class="bg-gray-500 text-white p-3 rounded-lg hover:bg-gray-600">
                Historique
            </button>
            <button onclick="testTemplate('settings')" class="bg-pink-500 text-white p-3 rounded-lg hover:bg-pink-600">
                Paramètres
            </button>
            <button onclick="testTemplate('invoices')" class="bg-orange-500 text-white p-3 rounded-lg hover:bg-orange-600">
                Factures
            </button>
            <button onclick="testTemplate('price-adjustment')" class="bg-yellow-500 text-white p-3 rounded-lg hover:bg-yellow-600">
                Prix
            </button>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Résultat du Test :</h2>
            <div id="status" class="mb-4 p-3 rounded-lg bg-gray-100">
                Cliquez sur un bouton pour tester le chargement d'un template
            </div>
            <div id="content" class="border border-gray-200 rounded-lg p-4 min-h-96 max-h-96 overflow-y-auto">
                <!-- Le contenu du template sera affiché ici -->
            </div>
        </div>
    </div>

    <script>
        async function testTemplate(templateName) {
            const statusDiv = document.getElementById('status');
            const contentDiv = document.getElementById('content');
            
            statusDiv.innerHTML = `⏳ Chargement du template: ${templateName}...`;
            statusDiv.className = 'mb-4 p-3 rounded-lg bg-yellow-100 text-yellow-800';
            
            try {
                const response = await fetch(`./templates/${templateName}.html`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const html = await response.text();
                
                statusDiv.innerHTML = `✅ Template "${templateName}" chargé avec succès !`;
                statusDiv.className = 'mb-4 p-3 rounded-lg bg-green-100 text-green-800';
                
                contentDiv.innerHTML = html;
                
            } catch (error) {
                statusDiv.innerHTML = `❌ Erreur lors du chargement de "${templateName}": ${error.message}`;
                statusDiv.className = 'mb-4 p-3 rounded-lg bg-red-100 text-red-800';
                
                contentDiv.innerHTML = `
                    <div class="text-center py-12 text-gray-500">
                        <div class="text-6xl mb-4">⚠️</div>
                        <h3 class="text-xl font-semibold mb-2">Erreur de chargement</h3>
                        <p>Impossible de charger le template "${templateName}"</p>
                        <p class="text-sm mt-2">Erreur: ${error.message}</p>
                    </div>
                `;
            }
        }

        // Test automatique au chargement
        window.addEventListener('load', () => {
            console.log('🧪 Page de test des templates chargée');
            console.log('📁 Chemin de base:', window.location.href);
        });
    </script>
</body>
</html>
