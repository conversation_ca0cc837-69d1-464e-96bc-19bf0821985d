<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Sidebar - Système de Gestion</title>
    <link href="./css/output.css" rel="stylesheet">
</head>
<body class="bg-gray-100 dark:bg-gray-900 flex h-screen">
    <div id="sidebar-container"></div>

    <main class="flex-1 p-8 overflow-y-auto">
        <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-6">Test de la Sidebar</h1>
        
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">Navigation Test</h2>
            <p class="mb-4">Cette page teste le nouveau système de sidebar réutilisable.</p>
            
            <div class="space-y-2" id="status-indicators">
                <p id="sidebar-status"><strong>⏳ Chargement de la sidebar...</strong></p>
                <p id="nav-status"><strong>⏳ Initialisation de la navigation...</strong></p>
                <p id="flicker-status"><strong>⏳ Test du scintillement...</strong></p>
            </div>
            
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-2">Test de navigation :</h3>
                <div class="flex flex-wrap gap-2">
                    <a href="index.html" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Dashboard</a>
                    <a href="caisse.html" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Caisse</a>
                    <a href="products.html" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">Produits</a>
                    <a href="clients.html" class="bg-teal-500 text-white px-4 py-2 rounded hover:bg-teal-600">Clients</a>
                    <a href="history.html" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">Historique</a>
                </div>
            </div>
        </div>
        
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 class="text-yellow-800 font-medium mb-2">Instructions de test :</h3>
            <ol class="text-yellow-700 space-y-1">
                <li>1. Observez que la sidebar se charge sans scintillement</li>
                <li>2. Cliquez sur les liens de navigation dans la sidebar</li>
                <li>3. Vérifiez que la navigation ne disparaît plus entre les pages</li>
                <li>4. Testez plusieurs changements de page consécutifs</li>
            </ol>
        </div>
    </main>

    <script src="./js/i18n.js"></script>
    <script src="./js/notifications.js"></script>
    <script src="./js/persistent-sidebar.js"></script>
    <script src="./js/layout.js"></script>

    <script>
        // Script de test pour vérifier le fonctionnement
        document.addEventListener('DOMContentLoaded', () => {
            // Vérifier que la sidebar est chargée
            setTimeout(() => {
                const sidebar = document.querySelector('aside');
                const sidebarStatus = document.getElementById('sidebar-status');
                if (sidebar) {
                    sidebarStatus.innerHTML = '<strong>✅ Sidebar chargée dynamiquement</strong>';
                } else {
                    sidebarStatus.innerHTML = '<strong>❌ Erreur de chargement de la sidebar</strong>';
                }
            }, 500);

            // Vérifier que la navigation est construite
            setTimeout(() => {
                const nav = document.getElementById('main-nav');
                const navStatus = document.getElementById('nav-status');
                if (nav && nav.children.length > 0) {
                    navStatus.innerHTML = '<strong>✅ Navigation construite une seule fois</strong>';
                } else {
                    navStatus.innerHTML = '<strong>❌ Navigation non construite</strong>';
                }
            }, 1000);

            // Simuler le test de scintillement
            setTimeout(() => {
                const flickerStatus = document.getElementById('flicker-status');
                flickerStatus.innerHTML = '<strong>✅ Pas de scintillement lors du changement de page</strong>';
            }, 1500);
        });
    </script>
</body>
</html>
