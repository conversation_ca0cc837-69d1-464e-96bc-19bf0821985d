// spa-router.js - Système de routage SPA pour éviter le rechargement de la sidebar

(function() {
    'use strict';

    // Configuration des routes
    const ROUTES = {
        'dashboard': {
            template: './templates/dashboard.html',
            title: 'dashboard_page_title',
            script: './js/dashboard.js',
            css: null
        },
        'caisse': {
            template: './templates/caisse.html',
            title: 'caisse_page_title',
            script: './js/caisse.js',
            css: null
        },
        'products': {
            template: './templates/products.html',
            title: 'products_page_title',
            script: './js/products.js',
            css: null
        },
        'clients': {
            template: './templates/clients.html',
            title: 'clients_page_title',
            script: './js/clients.js',
            css: null
        },
        'credits': {
            template: './templates/credits.html',
            title: 'credits_page_title',
            script: './js/credits.js',
            css: null
        },
        'history': {
            template: './templates/history.html',
            title: 'history_page_title',
            script: './js/history.js',
            css: null
        },
        'invoices': {
            template: './templates/invoices.html',
            title: 'invoices_page_title',
            script: './js/invoices.js',
            css: null
        },
        'settings': {
            template: './templates/settings.html',
            title: 'settings_page_title',
            script: './js/settings.js',
            css: null
        },
        'price-adjustment': {
            template: './templates/price-adjustment.html',
            title: 'price_adjustment_page_title',
            script: './js/price-adjustment.js',
            css: null
        },
        'stock-adjustment': {
            template: './templates/stock-adjustment.html',
            title: 'stock_adjustment_page_title',
            script: './js/stock-adjustment.js',
            css: null
        }
    };

    // État du routeur
    let currentRoute = null;
    let loadedScripts = new Set();
    let isNavigating = false;

    /**
     * Initialise le routeur SPA
     */
    async function initRouter() {
        console.log('🚀 Initialisation du routeur SPA');

        // Construire la navigation SPA une seule fois
        if (window.buildSPANavigation) {
            await window.buildSPANavigation('dashboard');
        }

        // Intercepter les clics sur les liens de navigation
        document.addEventListener('click', handleNavigationClick);

        // Gérer les changements d'historique du navigateur
        window.addEventListener('popstate', handlePopState);

        // Charger la route initiale selon le rôle de l'utilisateur
        const initialRoute = await getInitialRoute();
        navigateTo(initialRoute, false);

        // Initialiser le menu mobile
        initMobileMenu();
    }

    /**
     * Gère les clics sur les liens de navigation
     */
    function handleNavigationClick(event) {
        const link = event.target.closest('a[href]');
        if (!link) return;

        const href = link.getAttribute('href');
        
        // Vérifier si c'est un lien interne (page de l'app)
        if (href && !href.startsWith('http') && !href.startsWith('#') && href.endsWith('.html')) {
            event.preventDefault();
            
            // Extraire le nom de la route du href
            const routeName = href.replace('.html', '').replace('src/', '');
            const finalRoute = routeName === 'index' ? 'dashboard' : routeName;
            
            navigateTo(finalRoute);
        }
    }

    /**
     * Gère les changements d'historique du navigateur
     */
    function handlePopState(event) {
        const route = event.state?.route || getRouteFromURL() || 'dashboard';
        navigateTo(route, false);
    }

    /**
     * Navigue vers une route donnée
     */
    async function navigateTo(routeName, addToHistory = true) {
        if (isNavigating) {
            console.log('Navigation déjà en cours, ignorer...');
            return;
        }

        if (!ROUTES[routeName]) {
            console.error(`Route inconnue: ${routeName}`);
            routeName = 'dashboard'; // Fallback
        }

        console.log(`📍 Navigation vers: ${routeName}`);
        isNavigating = true;

        try {
            // Afficher le loader
            showLoader();

            // Mettre à jour l'historique du navigateur
            if (addToHistory) {
                const url = routeName === 'dashboard' ? '/' : `/${routeName}`;
                history.pushState({ route: routeName }, '', url);
            }

            // Charger le contenu de la page
            await loadPageContent(routeName);

            // Mettre à jour le titre de la page
            updatePageTitle(routeName);

            // Mettre à jour le lien actif dans la navigation
            updateActiveNavLink(routeName);

            // Mettre à jour la route actuelle
            currentRoute = routeName;

            console.log(`✅ Navigation vers ${routeName} terminée`);

        } catch (error) {
            console.error(`❌ Erreur lors de la navigation vers ${routeName}:`, error);
            showError('Erreur lors du chargement de la page');
        } finally {
            hideLoader();
            isNavigating = false;
        }
    }

    /**
     * Charge le contenu d'une page
     */
    async function loadPageContent(routeName) {
        const route = ROUTES[routeName];
        const contentContainer = document.getElementById('page-content');
        
        if (!contentContainer) {
            throw new Error('Conteneur de contenu non trouvé');
        }

        try {
            console.log(`🔄 Chargement du template: ${route.template}`);

            // Charger le template HTML
            const response = await fetch(route.template);
            console.log(`📡 Réponse fetch: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status} - ${response.statusText}`);
            }

            const html = await response.text();
            console.log(`📄 Template chargé, taille: ${html.length} caractères`);

            // Injecter le contenu avec une transition fluide
            contentContainer.classList.add('loading');

            setTimeout(() => {
                contentContainer.innerHTML = html;
                contentContainer.classList.remove('loading');
                console.log(`✅ Contenu injecté pour: ${routeName}`);

                // Charger le script spécifique à la page
                loadPageScript(route.script, routeName);

                // Réinitialiser les traductions pour le nouveau contenu
                if (window.i18n && window.i18n.updatePageTranslations) {
                    window.i18n.updatePageTranslations();
                }

            }, 100);

        } catch (error) {
            console.error(`❌ Erreur lors du chargement du template ${route.template}:`, error);
            console.error('📍 URL de base:', window.location.href);
            console.error('📍 Chemin template:', route.template);

            contentContainer.innerHTML = `
                <div class="text-center py-12">
                    <div class="text-red-500 text-6xl mb-4">⚠️</div>
                    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">Erreur de chargement</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-2">Impossible de charger le contenu de la page.</p>
                    <p class="text-sm text-gray-500 mb-4">Template: ${route.template}</p>
                    <p class="text-sm text-red-600 mb-4">Erreur: ${error.message}</p>
                    <button onclick="window.SPA_ROUTER.navigateTo('dashboard')"
                            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        Retour au tableau de bord
                    </button>
                </div>
            `;
        }
    }

    /**
     * Charge le script spécifique à une page
     */
    function loadPageScript(scriptPath, routeName) {
        if (!scriptPath || loadedScripts.has(scriptPath)) {
            // Script déjà chargé, juste réinitialiser la page
            reinitializePage(routeName);
            return;
        }

        const script = document.createElement('script');
        script.src = scriptPath;
        script.onload = () => {
            loadedScripts.add(scriptPath);
            reinitializePage(routeName);
            console.log(`✅ Script chargé: ${scriptPath}`);
        };
        script.onerror = () => {
            console.error(`❌ Erreur lors du chargement du script: ${scriptPath}`);
        };
        
        document.head.appendChild(script);
    }

    /**
     * Réinitialise la page après chargement du contenu
     */
    function reinitializePage(routeName) {
        // Déclencher l'événement de changement de page
        const event = new CustomEvent('spa:page-loaded', { 
            detail: { route: routeName } 
        });
        document.dispatchEvent(event);

        // Réinitialiser les composants spécifiques si nécessaire
        if (window.initializePage && typeof window.initializePage === 'function') {
            // Ne pas reconstruire la navigation, juste mettre à jour le lien actif
            if (window.updateActiveLink && typeof window.updateActiveLink === 'function') {
                window.updateActiveLink(routeName);
            }
        }
    }

    /**
     * Met à jour le titre de la page
     */
    function updatePageTitle(routeName) {
        const route = ROUTES[routeName];
        const t = window.i18n ? window.i18n.t : (key) => key;
        const title = t(route.title) || 'GestionPro';
        document.title = title;
    }

    /**
     * Met à jour le lien actif dans la navigation
     */
    function updateActiveNavLink(routeName) {
        if (window.updateSPAActiveLink && typeof window.updateSPAActiveLink === 'function') {
            window.updateSPAActiveLink(routeName);
        }
    }

    /**
     * Obtient la route depuis l'URL actuelle
     */
    function getRouteFromURL() {
        const path = window.location.pathname;
        if (path === '/' || path === '/index.html') return 'dashboard';
        return path.replace('/', '').replace('.html', '');
    }

    /**
     * Obtient la route initiale selon le rôle de l'utilisateur
     */
    async function getInitialRoute() {
        try {
            // Vérifier d'abord l'URL
            const urlRoute = getRouteFromURL();
            if (urlRoute && urlRoute !== 'dashboard' && ROUTES[urlRoute]) {
                return urlRoute;
            }

            // Sinon, déterminer selon le rôle de l'utilisateur
            if (window.api && window.api.session) {
                const user = await window.api.session.getCurrentUser();
                if (user && user.role === 'Vendeur') {
                    return 'caisse'; // Les vendeurs commencent sur la caisse
                }
            }

            return 'dashboard'; // Par défaut, dashboard pour les propriétaires
        } catch (error) {
            console.error('Erreur lors de la détermination de la route initiale:', error);
            return 'dashboard';
        }
    }

    /**
     * Affiche le loader
     */
    function showLoader() {
        const loader = document.querySelector('.page-loader');
        if (loader) {
            loader.classList.add('active');
        }
    }

    /**
     * Cache le loader
     */
    function hideLoader() {
        const loader = document.querySelector('.page-loader');
        if (loader) {
            loader.classList.remove('active');
        }
    }

    /**
     * Affiche un message d'erreur
     */
    function showError(message) {
        if (window.showNotification) {
            window.showNotification(message, 'error');
        } else {
            console.error(message);
        }
    }

    /**
     * Initialise le menu mobile
     */
    function initMobileMenu() {
        const mobileBtn = document.getElementById('mobile-menu-btn');
        const sidebar = document.querySelector('.sidebar-fixed');
        
        if (mobileBtn && sidebar) {
            mobileBtn.addEventListener('click', () => {
                sidebar.classList.toggle('mobile-open');
            });

            // Fermer le menu mobile lors du clic sur le contenu
            document.addEventListener('click', (event) => {
                if (!sidebar.contains(event.target) && !mobileBtn.contains(event.target)) {
                    sidebar.classList.remove('mobile-open');
                }
            });
        }
    }

    // Exposer l'API publique
    window.SPA_ROUTER = {
        init: initRouter,
        navigateTo: navigateTo,
        getCurrentRoute: () => currentRoute,
        getRoutes: () => ROUTES
    };

    // Auto-initialisation
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initRouter);
    } else {
        initRouter();
    }

})();
