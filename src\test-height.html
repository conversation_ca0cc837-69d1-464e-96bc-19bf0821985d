<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hauteur Sidebar</title>
    <link href="./css/output.css" rel="stylesheet">
    <style>
        /* Style pour visualiser la hauteur */
        body {
            margin: 0;
            padding: 0;
        }
        .debug-height {
            border: 2px solid red;
            position: relative;
        }
        .debug-height::after {
            content: 'HEIGHT: ' attr(data-height);
            position: absolute;
            top: 10px;
            right: 10px;
            background: red;
            color: white;
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 flex h-screen debug-height" data-height="h-screen">
    <div id="sidebar-container"></div>

    <main class="flex-1 p-8 overflow-y-auto debug-height" data-height="flex-1">
        <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-6">Test Hauteur Sidebar</h1>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Vérifications :</h2>
            <ul class="space-y-2">
                <li>✅ Body a la classe <code>h-screen</code></li>
                <li>✅ Sidebar a la classe <code>h-screen</code></li>
                <li>✅ Main a la classe <code>flex-1</code></li>
                <li>✅ Layout en <code>flex</code></li>
            </ul>
            
            <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded">
                <p class="text-blue-800 dark:text-blue-200">
                    La sidebar devrait maintenant prendre toute la hauteur de l'écran sans scintillement.
                </p>
            </div>
        </div>
    </main>

    <!-- Scripts nécessaires -->
    <script src="./js/i18n.js"></script>
    <script src="./js/layout.js"></script>
    <script src="./js/persistent-sidebar.js"></script>
    
    <script>
        // Debug : afficher les hauteurs calculées
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const body = document.body;
                const sidebar = document.querySelector('aside');
                const main = document.querySelector('main');
                
                console.log('=== DEBUG HAUTEURS ===');
                console.log('Body height:', body.offsetHeight + 'px');
                console.log('Window height:', window.innerHeight + 'px');
                
                if (sidebar) {
                    console.log('Sidebar height:', sidebar.offsetHeight + 'px');
                    console.log('Sidebar classes:', sidebar.className);
                }
                
                if (main) {
                    console.log('Main height:', main.offsetHeight + 'px');
                    console.log('Main classes:', main.className);
                }
                
                // Vérifier si les hauteurs sont correctes
                const isCorrect = sidebar && sidebar.offsetHeight >= window.innerHeight - 20; // -20 pour marge d'erreur
                console.log('Hauteur sidebar correcte:', isCorrect);
                
                if (!isCorrect) {
                    console.warn('⚠️ La sidebar ne prend pas toute la hauteur !');
                } else {
                    console.log('✅ Hauteur sidebar correcte !');
                }
            }, 1000);
        });
    </script>
</body>
</html>
