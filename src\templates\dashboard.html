<h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-6" data-i18n="dashboard_main_title">Tableau de Bord</h1>

<div class="mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md flex items-center justify-between">
    <div class="flex items-center gap-2">
        <button data-period="today" class="period-btn px-3 py-1 text-sm rounded-md" data-i18n="today">Aujourd'hui</button>
        <button data-period="week" class="period-btn px-3 py-1 text-sm rounded-md" data-i18n="this_week">Cette Semaine</button>
        <button data-period="month" class="period-btn px-3 py-1 text-sm rounded-md" data-i18n="this_month">Ce Mois-ci</button>
    </div>
    <div class="flex items-center gap-2 text-sm">
        <span data-i18n="from">De:</span>
        <input type="date" id="startDate" class="p-1 border rounded dark:bg-gray-700 dark:border-gray-600">
        <span data-i18n="to">à:</span>
        <input type="date" id="endDate" class="p-1 border rounded dark:bg-gray-700 dark:border-gray-600">
        <button id="filterByDate" class="px-3 py-1 text-sm rounded-md bg-blue-600 text-white" data-i18n="filter_button">Filtrer</button>
    </div>
</div>

<!-- Statistiques Principales -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold text-gray-500 dark:text-gray-400" data-i18n="turnover_sales">Chiffre d'Affaires (Ventes)</h2>
        <p class="text-4xl font-bold mt-2 text-green-600">
            <span id="revenue-stat">0.00</span> MAD
        </p>
    </div>

    <div id="profit-stat-card" class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hidden">
        <h2 class="text-lg font-semibold text-gray-500 dark:text-gray-400" data-i18n="net_profit">Bénéfice Net</h2>
        <p class="text-4xl font-bold mt-2 text-blue-500">
            <span id="profit-stat">0.00</span> MAD
        </p>
    </div>

    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold text-gray-500 dark:text-gray-400" data-i18n="credits_granted">Crédits Accordés</h2>
        <p class="text-4xl font-bold mt-2 text-orange-500">
            <span id="credit-stat">0.00</span> MAD
        </p>
    </div>
</div>

<!-- Section Analytics Produits -->
<div class="mb-8">
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
            <span class="mr-3">📊</span>
            <span data-i18n="product_analytics">Analytics Produits</span>
        </h2>
        <button id="exportAnalytics" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <span data-i18n="export_data">Exporter</span>
        </button>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Top Bénéfices Card -->
        <div class="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-xl shadow-lg border border-green-200 dark:border-green-800">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-green-800 dark:text-green-200 flex items-center">
                    <span class="mr-2">💰</span>
                    <span data-i18n="top_profitable_products">Produits les Plus Rentables</span>
                </h3>
                <div class="animate-pulse">
                    <div id="profitable-loading" class="w-4 h-4 bg-green-400 rounded-full hidden"></div>
                </div>
            </div>
            <div id="profitable-products" class="space-y-3">
                <!-- Contenu dynamique -->
            </div>
        </div>

        <!-- Top Ventes Card -->
        <div class="bg-gradient-to-br from-blue-50 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-xl shadow-lg border border-blue-200 dark:border-blue-800">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-blue-800 dark:text-blue-200 flex items-center">
                    <span class="mr-2">🔥</span>
                    <span data-i18n="top_selling_products">Produits les Plus Vendus</span>
                </h3>
                <div class="animate-pulse">
                    <div id="selling-loading" class="w-4 h-4 bg-blue-400 rounded-full hidden"></div>
                </div>
            </div>
            <div id="selling-products" class="space-y-3">
                <!-- Contenu dynamique -->
            </div>
        </div>

        <!-- Performance Globale Card -->
        <div class="bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl shadow-lg border border-purple-200 dark:border-purple-800">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-purple-800 dark:text-purple-200 flex items-center">
                    <span class="mr-2">📊</span>
                    <span data-i18n="performance_overview">Vue d'Ensemble</span>
                </h3>
                <button id="viewPerformanceDetails" class="text-sm text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-200 transition-colors">
                    <span data-i18n="view_details">Voir Détails</span>
                </button>
            </div>
            <div id="performance-overview" class="space-y-3">
                <!-- Contenu dynamique -->
            </div>
        </div>

        <!-- Insights Rapides Card -->
        <div class="bg-gradient-to-br from-yellow-50 to-orange-100 dark:from-yellow-900/20 dark:to-orange-900/20 p-6 rounded-xl shadow-lg border border-yellow-200 dark:border-yellow-800">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-yellow-800 dark:text-yellow-200 flex items-center">
                    <span class="mr-2">🎯</span>
                    <span data-i18n="quick_insights">Recommandations</span>
                </h3>
            </div>
            <div id="quick-insights" class="space-y-3">
                <!-- Contenu dynamique -->
            </div>
        </div>
    </div>
</div>
