<div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="clients_page_title">Gestion des Clients</h1>
    <button id="addClientBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition" data-i18n="add_client_button">
        Ajouter un Client
    </button>
</div>

<!-- Barre de recherche et filtres -->
<div class="mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
    <div class="flex flex-col md:flex-row gap-4 items-center">
        <div class="flex-1">
            <input type="text" id="clientSearch"
                   class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   data-i18n-placeholder="search_by_name_phone_ice"
                   placeholder="Rechercher par nom, téléphone ou ICE...">
        </div>
        <div class="flex gap-2">
            <button id="filterDebtors" class="px-4 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors" data-i18n="filter_debtors">
                Débiteurs
            </button>
            <button id="clearFilters" class="px-4 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors" data-i18n="clear_filters">
                Tout
            </button>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Clients</p>
                <p id="totalClients" class="text-lg font-semibold text-gray-900 dark:text-white">0</p>
            </div>
        </div>
    </div>
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <div class="flex items-center">
            <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Débiteurs</p>
                <p id="totalDebtors" class="text-lg font-semibold text-red-600 dark:text-red-400">0</p>
            </div>
        </div>
    </div>
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Crédit Total</p>
                <p id="totalCredit" class="text-lg font-semibold text-yellow-600 dark:text-yellow-400">0.00 MAD</p>
            </div>
        </div>
    </div>
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">À Jour</p>
                <p id="currentClients" class="text-lg font-semibold text-green-600 dark:text-green-400">0</p>
            </div>
        </div>
    </div>
</div>

<!-- Tableau des clients -->
<div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Client
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Contact
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        ICE
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Crédit
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Statut
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody id="clientsTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <!-- Contenu dynamique -->
            </tbody>
        </table>
    </div>
</div>

<!-- Modals seront ajoutés par le script JavaScript -->
