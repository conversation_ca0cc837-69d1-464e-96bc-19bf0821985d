// spa-templates.js - Templates intégrés pour éviter les problèmes CORS

(function() {
    'use strict';

    // Templates intégrés
    const TEMPLATES = {
        'dashboard': `
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-6" data-i18n="dashboard_main_title">Tableau de Bord</h1>

            <div class="mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <button data-period="today" class="period-btn px-3 py-1 text-sm rounded-md" data-i18n="today">Aujourd'hui</button>
                    <button data-period="week" class="period-btn px-3 py-1 text-sm rounded-md" data-i18n="this_week">Cette Semaine</button>
                    <button data-period="month" class="period-btn px-3 py-1 text-sm rounded-md" data-i18n="this_month">Ce Mois-ci</button>
                </div>
                <div class="flex items-center gap-2 text-sm">
                    <span data-i18n="from">De:</span>
                    <input type="date" id="startDate" class="p-1 border rounded dark:bg-gray-700 dark:border-gray-600">
                    <span data-i18n="to">à:</span>
                    <input type="date" id="endDate" class="p-1 border rounded dark:bg-gray-700 dark:border-gray-600">
                    <button id="filterByDate" class="px-3 py-1 text-sm rounded-md bg-blue-600 text-white" data-i18n="filter_button">Filtrer</button>
                </div>
            </div>

            <!-- Statistiques Principales -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                    <h2 class="text-lg font-semibold text-gray-500 dark:text-gray-400" data-i18n="turnover_sales">Chiffre d'Affaires (Ventes)</h2>
                    <p class="text-4xl font-bold mt-2 text-green-600">
                        <span id="revenue-stat">0.00</span> MAD
                    </p>
                </div>

                <div id="profit-stat-card" class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hidden">
                    <h2 class="text-lg font-semibold text-gray-500 dark:text-gray-400" data-i18n="net_profit">Bénéfice Net</h2>
                    <p class="text-4xl font-bold mt-2 text-blue-500">
                        <span id="profit-stat">0.00</span> MAD
                    </p>
                </div>

                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                    <h2 class="text-lg font-semibold text-gray-500 dark:text-gray-400" data-i18n="credits_granted">Crédits Accordés</h2>
                    <p class="text-4xl font-bold mt-2 text-orange-500">
                        <span id="credit-stat">0.00</span> MAD
                    </p>
                </div>
            </div>

            <!-- Section Analytics Produits -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
                        <span class="mr-3">📊</span>
                        <span data-i18n="product_analytics">Analytics Produits</span>
                    </h2>
                    <button id="exportAnalytics" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span data-i18n="export_data">Exporter</span>
                    </button>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Top Bénéfices Card -->
                    <div class="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-xl shadow-lg border border-green-200 dark:border-green-800">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-green-800 dark:text-green-200 flex items-center">
                                <span class="mr-2">💰</span>
                                <span data-i18n="top_profitable_products">Produits les Plus Rentables</span>
                            </h3>
                            <div class="animate-pulse">
                                <div id="profitable-loading" class="w-4 h-4 bg-green-400 rounded-full hidden"></div>
                            </div>
                        </div>
                        <div id="profitable-products" class="space-y-3">
                            <!-- Contenu dynamique -->
                        </div>
                    </div>

                    <!-- Top Ventes Card -->
                    <div class="bg-gradient-to-br from-blue-50 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-xl shadow-lg border border-blue-200 dark:border-blue-800">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-blue-800 dark:text-blue-200 flex items-center">
                                <span class="mr-2">🔥</span>
                                <span data-i18n="top_selling_products">Produits les Plus Vendus</span>
                            </h3>
                            <div class="animate-pulse">
                                <div id="selling-loading" class="w-4 h-4 bg-blue-400 rounded-full hidden"></div>
                            </div>
                        </div>
                        <div id="selling-products" class="space-y-3">
                            <!-- Contenu dynamique -->
                        </div>
                    </div>

                    <!-- Performance Globale Card -->
                    <div class="bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl shadow-lg border border-purple-200 dark:border-purple-800">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-purple-800 dark:text-purple-200 flex items-center">
                                <span class="mr-2">📊</span>
                                <span data-i18n="performance_overview">Vue d'Ensemble</span>
                            </h3>
                            <button id="viewPerformanceDetails" class="text-sm text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-200 transition-colors">
                                <span data-i18n="view_details">Voir Détails</span>
                            </button>
                        </div>
                        <div id="performance-overview" class="space-y-3">
                            <!-- Contenu dynamique -->
                        </div>
                    </div>

                    <!-- Insights Rapides Card -->
                    <div class="bg-gradient-to-br from-yellow-50 to-orange-100 dark:from-yellow-900/20 dark:to-orange-900/20 p-6 rounded-xl shadow-lg border border-yellow-200 dark:border-yellow-800">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-yellow-800 dark:text-yellow-200 flex items-center">
                                <span class="mr-2">🎯</span>
                                <span data-i18n="quick_insights">Recommandations</span>
                            </h3>
                        </div>
                        <div id="quick-insights" class="space-y-3">
                            <!-- Contenu dynamique -->
                        </div>
                    </div>
                </div>
            </div>
        `,

        'caisse': `
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="pos_title">💰 Point de Vente</h1>
                <div class="flex gap-3">
                    <button id="newSaleBtn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition">
                        Nouvelle Vente
                    </button>
                    <button id="historyBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                        Historique
                    </button>
                </div>
            </div>

            <!-- Interface de caisse -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Panneau de recherche et produits -->
                <div class="lg:col-span-2">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                        <div class="mb-4">
                            <input type="text" id="productSearch" 
                                   class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-blue-500"
                                   placeholder="Rechercher un produit par nom ou code-barres...">
                        </div>
                        
                        <!-- Grille des produits -->
                        <div id="productsGrid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
                            <!-- Produits seront chargés dynamiquement -->
                        </div>
                    </div>
                </div>

                <!-- Panier et total -->
                <div class="lg:col-span-1">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4">Panier</h2>
                        
                        <!-- Liste des articles -->
                        <div id="cartItems" class="space-y-2 mb-4 max-h-64 overflow-y-auto">
                            <!-- Articles du panier -->
                        </div>
                        
                        <!-- Total -->
                        <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                            <div class="flex justify-between items-center text-lg font-bold">
                                <span>Total:</span>
                                <span id="cartTotal" class="text-green-600">0.00 MAD</span>
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="mt-4 space-y-2">
                            <button id="cashPaymentBtn" class="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition font-semibold">
                                Paiement Espèces
                            </button>
                            <button id="creditPaymentBtn" class="w-full bg-orange-600 text-white py-3 rounded-lg hover:bg-orange-700 transition font-semibold">
                                Vente à Crédit
                            </button>
                            <button id="clearCartBtn" class="w-full bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 transition">
                                Vider le Panier
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistiques rapides -->
            <div class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Ventes Aujourd'hui</p>
                            <p id="todaySales" class="text-lg font-semibold text-gray-900 dark:text-white">0.00 MAD</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Transactions</p>
                            <p id="todayTransactions" class="text-lg font-semibold text-gray-900 dark:text-white">0</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
                    <div class="flex items-center">
                        <div class="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                            <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Crédits Accordés</p>
                            <p id="todayCredits" class="text-lg font-semibold text-gray-900 dark:text-white">0.00 MAD</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Moyenne/Transaction</p>
                            <p id="averageTransaction" class="text-lg font-semibold text-gray-900 dark:text-white">0.00 MAD</p>
                        </div>
                    </div>
                </div>
            </div>
        `,

        'credits': `
            <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-6 gap-4 flex-shrink-0">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2" data-i18n="debtor_clients_title">💳 Gestion des Crédits</h1>
                    <div id="creditStats" class="flex flex-wrap gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
                            <span class="text-gray-600 dark:text-gray-400">Total clients: <span id="totalClients" class="font-semibold text-gray-800 dark:text-white">0</span></span>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="w-3 h-3 bg-red-500 rounded-full"></span>
                            <span class="text-gray-600 dark:text-gray-400">Débiteurs: <span id="debtorClients" class="font-semibold text-red-600">0</span></span>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="w-3 h-3 bg-purple-500 rounded-full"></span>
                            <span class="text-gray-600 dark:text-gray-400">Crédit total: <span id="totalCredit" class="font-semibold text-purple-600">0 MAD</span></span>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <button id="manualCreditBtn" class="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-yellow-600 hover:to-yellow-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Nouveau Crédit
                    </button>
                    <button id="quickPaymentBtn" class="bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-3 rounded-lg font-semibold hover:from-green-700 hover:to-green-800 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        Encaisser
                    </button>
                </div>
            </div>

            <!-- Tableau des crédits -->
            <div class="flex-1 bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 flex flex-col">
                <div class="flex-1 overflow-y-auto" style="min-height: 600px; max-height: calc(100vh - 300px);">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 sticky top-0 z-10">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                    <div class="flex items-center gap-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        Client
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                    <div class="flex items-center gap-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                        Solde Crédit
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody id="debtorsTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Contenu dynamique -->
                        </tbody>
                    </table>
                </div>
            </div>
        `,

        'clients': `
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="clients_page_title">👥 Gestion des Clients</h1>
                <button id="addClientBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                    Ajouter un Client
                </button>
            </div>

            <!-- Tableau des clients -->
            <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Client
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Contact
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Crédit
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody id="clientsTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Contenu dynamique -->
                        </tbody>
                    </table>
                </div>
            </div>
        `,

        'products': `
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="products_page_title">📦 Gestion des Produits</h1>
                <button id="addProductBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                    Ajouter un Produit
                </button>
            </div>

            <!-- Tableau des produits -->
            <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Produit
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Prix
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Stock
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Contenu dynamique -->
                        </tbody>
                    </table>
                </div>
            </div>
        `,

        'history': `
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="history_page_title">📊 Historique des Ventes</h1>
                <div class="flex gap-3">
                    <button id="exportBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                        Exporter
                    </button>
                </div>
            </div>

            <!-- Tableau des ventes -->
            <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    ID Vente
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Date
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Montant
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody id="salesTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Contenu dynamique -->
                        </tbody>
                    </table>
                </div>
            </div>
        `,

        'settings': `
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="settings_page_title">⚙️ Paramètres</h1>
            </div>

            <!-- Section Apparence -->
            <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Paramètres d'Apparence</h2>

                <!-- Thème -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Thème</label>
                    <div class="grid grid-cols-3 gap-3">
                        <button id="lightTheme" class="theme-option p-3 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors">
                            <div class="w-full h-8 bg-white rounded mb-2"></div>
                            <span class="text-sm">Clair</span>
                        </button>
                        <button id="darkTheme" class="theme-option p-3 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors">
                            <div class="w-full h-8 bg-gray-800 rounded mb-2"></div>
                            <span class="text-sm">Sombre</span>
                        </button>
                        <button id="systemTheme" class="theme-option p-3 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors">
                            <div class="w-full h-8 bg-gradient-to-r from-white to-gray-800 rounded mb-2"></div>
                            <span class="text-sm">Système</span>
                        </button>
                    </div>
                </div>

                <!-- Langue -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Langue</label>
                    <select id="languageSelect" class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                        <option value="fr">Français</option>
                        <option value="ar">العربية</option>
                    </select>
                </div>
            </div>
        `,

        'invoices': `
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="invoices_page_title">📄 Gestion des Factures</h1>
                <button id="addInvoiceBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                    Nouvelle Facture
                </button>
            </div>

            <!-- Tableau des factures -->
            <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    N° Facture
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Client
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Montant
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody id="invoicesTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Contenu dynamique -->
                        </tbody>
                    </table>
                </div>
            </div>
        `,

        'price-adjustment': `
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="price_adjustment_page_title">💰 Ajustement des Prix</h1>
                <button id="bulkUpdateBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                    Mise à jour en lot
                </button>
            </div>

            <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
                <p class="text-gray-600 dark:text-gray-400 text-center py-8">
                    Interface d'ajustement des prix en cours de développement...
                </p>
            </div>
        `,

        'stock-adjustment': `
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="stock_adjustment_page_title">📦 Ajustement du Stock</h1>
                <button id="bulkAdjustmentBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                    Ajustement en lot
                </button>
            </div>

            <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
                <p class="text-gray-600 dark:text-gray-400 text-center py-8">
                    Interface d'ajustement du stock en cours de développement...
                </p>
            </div>
        `
    };

    // Exposer les templates globalement
    window.SPA_TEMPLATES = TEMPLATES;

})();
