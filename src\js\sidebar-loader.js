// sidebar-loader.js - Chargeur de sidebar réutilisable

// Variables globales pour suivre l'état de la sidebar
let sidebarLoaded = false;
let sidebarInitialized = false;
let globalSidebarElement = null;

/**
 * Crée ou récupère la sidebar globale
 * @returns {HTMLElement} - L'élément sidebar
 */
function createGlobalSidebar() {
    if (globalSidebarElement) {
        return globalSidebarElement;
    }

    // Créer la sidebar
    const aside = document.createElement('aside');
    aside.className = 'w-64 bg-gray-800 text-white flex flex-col flex-shrink-0';
    aside.innerHTML = `
        <div class="p-4 text-2xl font-bold border-b border-gray-700">GestionPro</div>
        <nav id="main-nav" class="flex-grow"></nav>
    `;

    globalSidebarElement = aside;
    return aside;
}

/**
 * Charge la sidebar une seule fois et l'insère dans le conteneur spécifié
 * @param {string} containerId - L'ID du conteneur où insérer la sidebar
 * @returns {Promise<boolean>} - True si le chargement a réussi, false sinon
 */
async function loadSidebar(containerId = 'sidebar-container') {
    try {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`Conteneur avec l'ID "${containerId}" non trouvé`);
            return false;
        }

        // Si la sidebar est déjà présente dans ce conteneur, ne rien faire
        if (container.querySelector('aside')) {
            console.log('Sidebar déjà présente dans le conteneur');
            return true;
        }

        // Si nous avons une sidebar globale, l'utiliser
        if (globalSidebarElement) {
            // Retirer la sidebar de son conteneur actuel s'il existe
            if (globalSidebarElement.parentNode) {
                globalSidebarElement.parentNode.removeChild(globalSidebarElement);
            }
            container.appendChild(globalSidebarElement);
            console.log('Sidebar globale réutilisée');
            return true;
        }

        // Créer une nouvelle sidebar
        const sidebar = createGlobalSidebar();
        container.appendChild(sidebar);

        sidebarLoaded = true;
        console.log('Nouvelle sidebar créée et chargée');
        return true;

    } catch (error) {
        console.error('Erreur lors du chargement de la sidebar:', error);
        return false;
    }
}

/**
 * Initialise la sidebar et la navigation pour une page donnée
 * @param {string} activePage - Le nom de la page active
 * @param {string} containerId - L'ID du conteneur de la sidebar
 */
async function initializeSidebar(activePage, containerId = 'sidebar-container') {
    try {
        // Charger la sidebar seulement si elle n'est pas déjà chargée
        const loaded = await loadSidebar(containerId);

        if (!loaded) {
            console.error('Impossible de charger la sidebar');
            return;
        }

        // Attendre un peu pour s'assurer que le DOM est mis à jour
        await new Promise(resolve => setTimeout(resolve, 50));

        // Initialiser la navigation seulement si elle n'est pas déjà initialisée
        if (!sidebarInitialized) {
            if (window.initializePage) {
                await window.initializePage(activePage);
                sidebarInitialized = true;
            } else if (window.buildNavigation) {
                await window.buildNavigation(activePage);
                sidebarInitialized = true;
            } else {
                console.warn('Fonctions de navigation non disponibles');
            }
        } else {
            // Sidebar déjà initialisée, juste mettre à jour le lien actif
            if (window.updateActiveLink) {
                window.updateActiveLink(activePage);
            }
            console.log('Sidebar déjà initialisée, mise à jour du lien actif seulement');
        }

    } catch (error) {
        console.error('Erreur lors de l\'initialisation de la sidebar:', error);
    }
}

/**
 * Fonction d'initialisation rapide pour les pages
 * À appeler dans chaque page après le chargement du DOM
 */
function initPageWithSidebar() {
    // Déterminer le nom de la page actuelle
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '') || 'index';
    
    // Initialiser la sidebar
    initializeSidebar(currentPage);
}

/**
 * Réinitialise l'état de la sidebar (utile pour le débogage)
 */
function resetSidebarState() {
    sidebarLoaded = false;
    sidebarInitialized = false;
    console.log('État de la sidebar réinitialisé');
}

// Exposer les fonctions globalement
window.loadSidebar = loadSidebar;
window.initializeSidebar = initializeSidebar;
window.initPageWithSidebar = initPageWithSidebar;
window.resetSidebarState = resetSidebarState;

// Auto-initialisation si le DOM est déjà chargé
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPageWithSidebar);
} else {
    // DOM déjà chargé, initialiser immédiatement
    initPageWithSidebar();
}
