<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self'">
    <title data-i18n="invoicing_page_title">Facturation - Système de Gestion</title>
    <link href="./css/output.css" rel="stylesheet">
    <style>
        .search-results-container { position: absolute; z-index: 10; width: 100%; max-height: 150px; overflow-y: auto; background-color: white; border: 1px solid #d1d5db; border-radius: 0 0 0.5rem 0.5rem; box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }
        .dark .search-results-container { background-color: #374151; border-color: #4b5563; }
        .search-result-item { padding: 0.5rem; cursor: pointer; }
        .search-result-item:hover { background-color: #f3f4f6; }
        .dark .search-result-item:hover { background-color: #4b5563; }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 flex h-screen">
    <div id="sidebar-container"></div>

    <main class="flex-1 p-8 overflow-y-auto">
        <div id="listView">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="invoices_main_title">Factures</h1>
                <button id="newInvoiceBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700" data-i18n="new_invoice_button">Nouvelle Facture</button>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider" data-i18n="invoice_no_header">N° Facture</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider" data-i18n="date_header">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider" data-i18n="client_label">Client</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider" data-i18n="amount_header">Montant</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider" data-i18n="actions_header">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="invoicesTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"></tbody>
                </table>
            </div>
        </div>

        <div id="editorView" class="hidden">
            <div class="flex justify-between items-center mb-6">
                <h1 id="editorTitle" class="text-3xl font-bold text-gray-800 dark:text-white">Créer une Facture</h1>
                <div>
                    <button id="backToListBtn" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600" data-i18n="back_button">Retour</button>
                    <button id="saveInvoiceBtn" class="bg-green-600 text-white px-4 py-2 rounded-lg ml-2 hover:bg-green-700" data-i18n="save_button">Sauvegarder</button>
                    <button id="printInvoiceBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg ml-2 hover:bg-blue-700 hidden" data-i18n="print_pdf_button">Imprimer/PDF</button>
                </div>
            </div>
            <div id="invoice-editor" class="bg-white dark:bg-gray-800 p-8 shadow-lg rounded-lg">
            </div>
        </div>
    </main>
    
    <div id="confirmationModal"></div>

    <script src="./js/i18n.js"></script>
    <script src="./js/notifications.js"></script>
    <script src="./js/persistent-sidebar.js"></script>
    <script src="./js/layout.js"></script>
    <script src="./js/invoices.js"></script>
</body>
</html>