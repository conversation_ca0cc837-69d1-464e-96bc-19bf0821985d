<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test SPA Corrigé - GestionPro</title>
    <link href="./css/output.css" rel="stylesheet">
    <style>
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 1000;
            max-width: 300px;
        }
        
        .test-navigation {
            position: fixed;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
        }
        
        .test-btn {
            margin: 2px;
            padding: 8px 12px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .test-btn:hover {
            background: #2563eb;
        }
        
        .test-btn.active {
            background: #059669;
        }

        /* Styles pour les transitions de page */
        #page-content {
            transition: opacity 0.2s ease-in-out;
        }

        #page-content.loading {
            opacity: 0.7;
        }

        /* Loader pour les transitions */
        .page-loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
        }

        .page-loader.active {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Sidebar fixe */
        .sidebar-fixed {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 10;
        }

        .content-with-sidebar {
            margin-left: 16rem; /* w-64 = 16rem */
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar-fixed {
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
            }

            .sidebar-fixed.mobile-open {
                transform: translateX(0);
            }

            .content-with-sidebar {
                margin-left: 0;
            }
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 min-h-screen">
    <!-- Info de test -->
    <div class="test-info">
        <div><strong>🧪 Test SPA Corrigé</strong></div>
        <div>Route actuelle: <span id="currentRoute">-</span></div>
        <div>Templates intégrés: <span id="templatesStatus">❌</span></div>
        <div>Navigation construite: <span id="navStatus">❌</span></div>
        <div>Contenu chargé: <span id="contentStatus">❌</span></div>
    </div>

    <!-- Navigation de test -->
    <div class="test-navigation">
        <div style="text-align: center; margin-bottom: 8px; font-weight: bold;">🚀 Test Navigation SPA (Templates Intégrés)</div>
        <button class="test-btn" onclick="testNavigate('dashboard')">Dashboard</button>
        <button class="test-btn" onclick="testNavigate('caisse')">Caisse</button>
        <button class="test-btn" onclick="testNavigate('products')">Produits</button>
        <button class="test-btn" onclick="testNavigate('clients')">Clients</button>
        <button class="test-btn" onclick="testNavigate('credits')">Crédits</button>
        <button class="test-btn" onclick="testNavigate('history')">Historique</button>
        <button class="test-btn" onclick="testNavigate('settings')">Paramètres</button>
        <button class="test-btn" onclick="testNavigate('invoices')">Factures</button>
        <button class="test-btn" onclick="testNavigate('price-adjustment')">Prix</button>
        <button class="test-btn" onclick="testNavigate('stock-adjustment')">Stock</button>
    </div>

    <!-- Sidebar fixe -->
    <aside class="sidebar-fixed w-64 h-screen bg-gray-800 text-white flex flex-col flex-shrink-0">
        <div class="p-4 text-2xl font-bold border-b border-gray-700">GestionPro</div>
        <nav id="main-nav" class="flex-grow"></nav>
    </aside>

    <!-- Bouton menu mobile -->
    <button id="mobile-menu-btn" class="md:hidden fixed top-4 left-4 z-20 bg-gray-800 text-white p-2 rounded-lg">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
    </button>

    <!-- Contenu principal -->
    <main class="content-with-sidebar min-h-screen">
        <!-- Loader pour les transitions -->
        <div class="page-loader">
            <div class="spinner"></div>
        </div>

        <!-- Conteneur du contenu de la page -->
        <div id="page-content" class="p-8">
            <div class="text-center py-12">
                <div class="text-6xl mb-4">✅</div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-4">SPA Corrigé - Templates Intégrés</h1>
                <p class="text-gray-600 dark:text-gray-400 mb-6">
                    Les templates sont maintenant intégrés dans le JavaScript pour éviter les problèmes CORS.<br>
                    Testez la navigation avec les boutons en bas !
                </p>
                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4 max-w-md mx-auto">
                    <h3 class="font-semibold text-green-800 dark:text-green-200 mb-2">✅ Corrections apportées :</h3>
                    <ul class="text-sm text-green-700 dark:text-green-300 text-left space-y-1">
                        <li>• Templates intégrés dans spa-templates.js</li>
                        <li>• Plus de problèmes CORS/fetch</li>
                        <li>• Navigation fluide garantie</li>
                        <li>• Sidebar reste fixe</li>
                        <li>• Fallback pour templates manquants</li>
                    </ul>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script>
        // Mock de l'API pour les tests
        window.api = {
            session: {
                getCurrentUser: () => Promise.resolve({ 
                    id: 1, 
                    username: 'test', 
                    role: 'Propriétaire' 
                })
            }
        };

        // Mock de i18n
        window.i18n = {
            t: (key) => {
                const translations = {
                    'main_menu_dashboard': 'Tableau de Bord',
                    'main_menu_cash_register': 'Caisse',
                    'main_menu_products': 'Produits',
                    'main_menu_clients': 'Clients',
                    'main_menu_credits': 'Crédits',
                    'main_menu_history': 'Historique',
                    'main_menu_settings': 'Paramètres',
                    'main_menu_price_adjustment': 'Ajustement Prix',
                    'main_menu_stock_adjustment': 'Ajustement Stock',
                    'main_menu_invoices': 'Factures'
                };
                return translations[key] || key;
            }
        };

        // Fonctions de test
        function testNavigate(route) {
            if (window.SPA_ROUTER) {
                window.SPA_ROUTER.navigateTo(route);
                updateTestStatus();
                
                // Mettre à jour les boutons actifs
                document.querySelectorAll('.test-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                event.target.classList.add('active');
            }
        }

        function updateTestStatus() {
            // Route actuelle
            const currentRoute = window.SPA_ROUTER ? window.SPA_ROUTER.getCurrentRoute() : 'Non définie';
            document.getElementById('currentRoute').textContent = currentRoute;

            // Templates intégrés
            const hasTemplates = window.SPA_TEMPLATES && Object.keys(window.SPA_TEMPLATES).length > 0;
            document.getElementById('templatesStatus').textContent = hasTemplates ? '✅' : '❌';

            // Navigation
            const nav = document.getElementById('main-nav');
            const hasNavItems = nav && nav.children.length > 0;
            document.getElementById('navStatus').textContent = hasNavItems ? '✅' : '❌';

            // Contenu
            const content = document.getElementById('page-content');
            const hasContent = content && content.innerHTML.trim().length > 0;
            document.getElementById('contentStatus').textContent = hasContent ? '✅' : '❌';
        }

        // Écouter les changements de page
        document.addEventListener('spa:page-loaded', (event) => {
            console.log('🎉 Page SPA chargée:', event.detail.route);
            updateTestStatus();
        });

        // Mise à jour initiale
        setTimeout(updateTestStatus, 1000);
    </script>

    <!-- Scripts de l'application -->
    <script src="./js/i18n.js"></script>
    <script src="./js/preloader.js"></script>
    <script src="./js/notifications.js"></script>
    <script src="./js/layout.js"></script>
    <script src="./js/spa-templates.js"></script>
    <script src="./js/spa-navigation.js"></script>
    <script src="./js/spa-router.js"></script>
</body>
</html>
