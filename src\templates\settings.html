<div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="settings_page_title">⚙️ Paramètres</h1>
</div>

<!-- Navigation des sections -->
<div class="mb-6">
    <nav class="flex space-x-8" aria-label="Tabs">
        <button id="appearanceTab" class="section-nav-btn active whitespace-nowrap py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
            Apparence
        </button>
        <button id="companyTab" class="section-nav-btn whitespace-nowrap py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
            Entreprise
        </button>
        <button id="securityTab" class="section-nav-btn whitespace-nowrap py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
            Sécurité
        </button>
        <button id="usersTab" class="section-nav-btn whitespace-nowrap py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
            Utilisateurs
        </button>
    </nav>
</div>

<!-- Section Apparence -->
<div id="appearance-section" class="settings-section">
    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Paramètres d'Apparence</h2>
        
        <!-- Thème -->
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Thème</label>
            <div class="grid grid-cols-3 gap-3">
                <button id="lightTheme" class="theme-option p-3 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors">
                    <div class="w-full h-8 bg-white rounded mb-2"></div>
                    <span class="text-sm">Clair</span>
                </button>
                <button id="darkTheme" class="theme-option p-3 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors">
                    <div class="w-full h-8 bg-gray-800 rounded mb-2"></div>
                    <span class="text-sm">Sombre</span>
                </button>
                <button id="systemTheme" class="theme-option p-3 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors">
                    <div class="w-full h-8 bg-gradient-to-r from-white to-gray-800 rounded mb-2"></div>
                    <span class="text-sm">Système</span>
                </button>
            </div>
        </div>

        <!-- Langue -->
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Langue</label>
            <select id="languageSelect" class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                <option value="fr">Français</option>
                <option value="ar">العربية</option>
            </select>
        </div>
    </div>
</div>

<!-- Section Entreprise -->
<div id="company-section" class="settings-section hidden">
    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Informations de l'Entreprise</h2>
        
        <form id="companyForm" class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Nom de l'entreprise</label>
                <input type="text" id="companyName" class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Adresse</label>
                <textarea id="companyAddress" rows="3" class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600"></textarea>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Téléphone</label>
                    <input type="tel" id="companyPhone" class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email</label>
                    <input type="email" id="companyEmail" class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                </div>
            </div>
            
            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                Sauvegarder
            </button>
        </form>
    </div>
</div>

<!-- Section Sécurité -->
<div id="security-section" class="settings-section hidden">
    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Paramètres de Sécurité</h2>
        
        <div class="space-y-6">
            <div>
                <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-2">Changer le mot de passe</h3>
                <form id="passwordForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Mot de passe actuel</label>
                        <input type="password" id="currentPassword" class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Nouveau mot de passe</label>
                        <input type="password" id="newPassword" class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Confirmer le nouveau mot de passe</label>
                        <input type="password" id="confirmPassword" class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                    </div>
                    <button type="submit" class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition">
                        Changer le mot de passe
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Section Utilisateurs -->
<div id="users-section" class="settings-section hidden">
    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Gestion des Utilisateurs</h2>
            <button id="addUserBtn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition">
                Ajouter un Utilisateur
            </button>
        </div>
        
        <!-- Liste des utilisateurs -->
        <div id="usersList" class="space-y-3">
            <!-- Contenu dynamique -->
        </div>
    </div>
</div>
