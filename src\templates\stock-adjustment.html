<div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-800 dark:text-white" data-i18n="stock_adjustment_page_title">📦 Ajustement du Stock</h1>
    <button id="bulkAdjustmentBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
        Ajustement en lot
    </button>
</div>

<!-- Barre de recherche et filtres -->
<div class="mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
    <div class="flex flex-col md:flex-row gap-4 items-center">
        <div class="flex-1">
            <input type="text" id="productSearch"
                   class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-blue-500"
                   placeholder="Rechercher un produit par nom ou code-barres...">
        </div>
        <div class="flex gap-2">
            <select id="categoryFilter" class="px-4 py-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                <option value="">Toutes catégories</option>
            </select>
            <button id="lowStockFilter" class="px-4 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition">
                Stock Faible
            </button>
            <button id="saveChangesBtn" class="px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition">
                Sauvegarder
            </button>
        </div>
    </div>
</div>

<!-- Outils d'ajustement rapide -->
<div class="mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
    <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4">⚡ Ajustement Rapide</h3>
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Type d'ajustement</label>
            <select id="adjustmentType" class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                <option value="add">Ajouter au stock</option>
                <option value="remove">Retirer du stock</option>
                <option value="set">Définir le stock</option>
            </select>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quantité</label>
            <input type="number" id="adjustmentQuantity" min="0" class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600" placeholder="Ex: 50">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Motif</label>
            <select id="adjustmentReason" class="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                <option value="restock">Réapprovisionnement</option>
                <option value="damage">Produit endommagé</option>
                <option value="theft">Vol/Perte</option>
                <option value="inventory">Inventaire</option>
                <option value="return">Retour client</option>
                <option value="other">Autre</option>
            </select>
        </div>
        <div class="flex items-end">
            <button id="applyAdjustmentBtn" class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition">
                Appliquer
            </button>
        </div>
    </div>
</div>

<!-- Statistiques -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Produits Modifiés</p>
                <p id="modifiedCount" class="text-lg font-semibold text-gray-900 dark:text-white">0</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <div class="flex items-center">
            <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Stock Faible</p>
                <p id="lowStockCount" class="text-lg font-semibold text-red-600 dark:text-red-400">0</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Valeur Ajoutée</p>
                <p id="addedValue" class="text-lg font-semibold text-green-600 dark:text-green-400">0.00 MAD</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Valeur Totale Stock</p>
                <p id="totalStockValue" class="text-lg font-semibold text-purple-600 dark:text-purple-400">0.00 MAD</p>
            </div>
        </div>
    </div>
</div>

<!-- Tableau des produits -->
<div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        <input type="checkbox" id="selectAll" class="rounded border-gray-300">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Produit
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Stock Actuel
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Nouveau Stock
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Différence
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Motif
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody id="productsTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <!-- Contenu dynamique -->
            </tbody>
        </table>
    </div>
</div>

<!-- Modals seront ajoutés par le script JavaScript -->
